#!/usr/bin/env python3
"""
Conversation ID日志追踪功能演示脚本

展示conversation_id在不同场景下的使用效果，
包括自动模式、手动模式、多线程隔离等。
"""

import threading
import time
from src.utils.conversation_context import (
    conversation_context,
    set_current_conversation_id,
    clear_current_conversation_id,
    generate_conversation_id
)
from src.utils.enhanced_logger import get_enhanced_logger


def demo_basic_usage():
    """演示基础使用方式"""
    print("\n" + "="*60)
    print("🎯 演示1: 基础使用方式")
    print("="*60)
    
    logger = get_enhanced_logger("demo_basic")
    
    # 没有conversation_id的日志
    logger.info("这是没有conversation_id的日志")
    
    # 设置conversation_id后的日志
    conv_id = generate_conversation_id()
    set_current_conversation_id(conv_id)
    logger.info("这是有conversation_id的日志")
    logger.warning("警告信息也会包含conversation_id")
    logger.error("错误信息同样包含conversation_id")
    
    # 清理后的日志
    clear_current_conversation_id()
    logger.info("清理后又没有conversation_id了")


def demo_context_manager():
    """演示上下文管理器使用方式"""
    print("\n" + "="*60)
    print("🎯 演示2: 上下文管理器")
    print("="*60)
    
    logger = get_enhanced_logger("demo_context")
    
    logger.info("上下文外的日志")
    
    conv_id = generate_conversation_id()
    with conversation_context.conversation_scope(conv_id):
        logger.info("上下文内的日志 - 自动包含conversation_id")
        logger.info("多条日志都会包含相同的conversation_id")
        
        # 嵌套上下文
        nested_conv_id = generate_conversation_id()
        with conversation_context.conversation_scope(nested_conv_id):
            logger.info("嵌套上下文内的日志 - 使用新的conversation_id")
        
        logger.info("回到外层上下文 - 恢复原来的conversation_id")
    
    logger.info("退出上下文后的日志 - 又没有conversation_id了")


def demo_thread_isolation():
    """演示多线程隔离"""
    print("\n" + "="*60)
    print("🎯 演示3: 多线程隔离")
    print("="*60)
    
    def worker_thread(thread_name: str, conversation_id: str):
        """工作线程函数"""
        logger = get_enhanced_logger(f"demo_thread_{thread_name}")
        
        # 设置当前线程的conversation_id
        set_current_conversation_id(conversation_id)
        
        # 模拟一些工作
        for i in range(3):
            logger.info(f"线程 {thread_name} 的第 {i+1} 条日志")
            time.sleep(0.1)
        
        logger.info(f"线程 {thread_name} 工作完成")
    
    # 创建多个线程，每个使用不同的conversation_id
    threads = []
    for i in range(3):
        conv_id = f"thread_{i}_{generate_conversation_id()}"
        thread = threading.Thread(
            target=worker_thread, 
            args=(f"worker_{i}", conv_id),
            name=f"demo_worker_{i}"
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print("所有线程完成，注意每个线程的日志都有独立的conversation_id")


def demo_offline_task_protection():
    """演示离线任务保护"""
    print("\n" + "="*60)
    print("🎯 演示4: 离线任务保护")
    print("="*60)
    
    def simulate_offline_task(task_name: str):
        """模拟离线任务"""
        # 修改线程名称以模拟离线任务
        original_name = threading.current_thread().name
        threading.current_thread().name = task_name
        
        logger = get_enhanced_logger(f"demo_{task_name}")
        
        try:
            # 尝试设置conversation_id（但不会生效）
            set_current_conversation_id("should_not_appear")
            
            logger.info(f"{task_name} 开始执行")
            logger.info(f"{task_name} 正在处理数据")
            logger.info(f"{task_name} 执行完成")
            
        finally:
            # 恢复原始线程名称
            threading.current_thread().name = original_name
    
    # 模拟不同类型的离线任务
    offline_tasks = [
        "token_refresh_worker",
        "session_cleanup_daemon", 
        "db_monitor_background"
    ]
    
    for task_name in offline_tasks:
        simulate_offline_task(task_name)
    
    print("注意：离线任务的日志都没有conversation_id，这是正确的保护机制")


def demo_exception_handling():
    """演示异常处理"""
    print("\n" + "="*60)
    print("🎯 演示5: 异常处理")
    print("="*60)
    
    logger = get_enhanced_logger("demo_exception")
    
    conv_id = generate_conversation_id()
    
    try:
        with conversation_context.conversation_scope(conv_id):
            logger.info("开始执行可能出错的操作")
            logger.warning("检测到潜在问题")
            
            # 模拟异常
            raise ValueError("模拟的业务异常")
            
    except ValueError as e:
        logger.error(f"捕获到异常: {e}")
    
    # 检查异常后conversation_id是否被正确清理
    logger.info("异常处理完成，这条日志应该没有conversation_id")


def demo_real_world_scenario():
    """演示真实世界场景"""
    print("\n" + "="*60)
    print("🎯 演示6: 真实世界场景模拟")
    print("="*60)
    
    def simulate_user_request(user_name: str, query: str):
        """模拟用户请求处理"""
        # 生成conversation_id（模拟API入口）
        conv_id = generate_conversation_id()
        
        logger = get_enhanced_logger("chatbi_api")
        
        with conversation_context.conversation_scope(conv_id):
            logger.info(f"收到用户 {user_name} 的查询: {query}")
            
            # 模拟查询处理过程
            logger.info("开始分析用户查询")
            time.sleep(0.1)
            
            logger.info("调用Agent处理查询")
            time.sleep(0.1)
            
            logger.info("执行数据库查询")
            time.sleep(0.1)
            
            logger.info("生成响应结果")
            time.sleep(0.1)
            
            logger.info(f"用户 {user_name} 的查询处理完成")
    
    # 模拟多个用户的并发请求
    users_queries = [
        ("张三", "查询本月销售数据"),
        ("李四", "分析库存情况"),
        ("王五", "生成财务报表")
    ]
    
    threads = []
    for user_name, query in users_queries:
        thread = threading.Thread(
            target=simulate_user_request,
            args=(user_name, query),
            name=f"api_request_{user_name}"
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有请求完成
    for thread in threads:
        thread.join()
    
    print("真实场景演示完成，每个用户请求都有独立的conversation_id追踪")


def main():
    """主演示函数"""
    print("🚀 Conversation ID日志追踪功能演示")
    print("本演示将展示conversation_id在各种场景下的使用效果")
    
    # 运行各种演示
    demo_basic_usage()
    demo_context_manager()
    demo_thread_isolation()
    demo_offline_task_protection()
    demo_exception_handling()
    demo_real_world_scenario()
    
    print("\n" + "="*60)
    print("🎉 演示完成！")
    print("="*60)
    print("总结:")
    print("1. ✅ 基础功能：自动在日志中添加conversation_id")
    print("2. ✅ 上下文管理：支持嵌套和自动清理")
    print("3. ✅ 线程隔离：不同线程使用独立的conversation_id")
    print("4. ✅ 离线保护：离线任务不会被污染conversation_id")
    print("5. ✅ 异常处理：异常情况下正确清理上下文")
    print("6. ✅ 真实场景：完整的请求处理链路追踪")
    print("\nConversation ID日志追踪功能已成功集成到ChatBI系统！")


if __name__ == "__main__":
    main()
