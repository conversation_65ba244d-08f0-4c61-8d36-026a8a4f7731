#!/usr/bin/env python3
"""
Conversation ID日志追踪功能测试脚本

测试conversation_id在不同场景下的正确设置、传递和清理，
确保线程隔离和生命周期管理的正确性。

测试场景：
1. 基础功能测试：上下文设置、获取、清理
2. 线程隔离测试：多线程环境下的隔离性
3. 日志格式化测试：日志中conversation_id的正确显示
4. 离线任务保护测试：离线任务不受影响
5. 异常处理测试：异常情况下的上下文清理
6. 性能测试：上下文管理的性能影响
"""

import threading
import time
import uuid
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

# 导入我们的模块
from src.utils.conversation_context import (
    conversation_context,
    get_current_conversation_id,
    set_current_conversation_id,
    clear_current_conversation_id,
    generate_conversation_id,
    is_offline_task,
    safe_get_conversation_id
)
from src.utils.enhanced_logger import get_enhanced_logger


class ConversationIdTester:
    """Conversation ID功能测试器"""
    
    def __init__(self):
        self.logger = get_enhanced_logger("test_conversation_id")
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")
        
    def test_basic_functionality(self):
        """测试基础功能：设置、获取、清理"""
        print("\n=== 测试基础功能 ===")

        # 确保测试开始前状态干净
        clear_current_conversation_id()

        # 测试初始状态
        initial_id = get_current_conversation_id()
        self.log_test_result(
            "初始状态检查",
            initial_id is None,
            f"初始conversation_id应为None，实际: {initial_id}"
        )
        
        # 测试设置conversation_id
        test_id = generate_conversation_id()
        set_current_conversation_id(test_id)
        retrieved_id = get_current_conversation_id()
        self.log_test_result(
            "设置和获取conversation_id",
            retrieved_id == test_id,
            f"设置: {test_id}, 获取: {retrieved_id}"
        )
        
        # 测试日志包含conversation_id
        self.logger.info("这是一条测试日志，应该包含conversation_id")
        
        # 测试清理
        clear_current_conversation_id()
        cleared_id = get_current_conversation_id()
        self.log_test_result(
            "清理conversation_id",
            cleared_id is None,
            f"清理后应为None，实际: {cleared_id}"
        )
        
    def test_thread_isolation(self):
        """测试线程隔离"""
        print("\n=== 测试线程隔离 ===")
        
        results = {}
        
        def worker_thread(thread_id: int, conversation_id: str):
            """工作线程函数"""
            try:
                # 设置当前线程的conversation_id
                set_current_conversation_id(conversation_id)
                
                # 记录设置后的ID
                retrieved_id = get_current_conversation_id()
                results[thread_id] = {
                    "set_id": conversation_id,
                    "retrieved_id": retrieved_id,
                    "success": retrieved_id == conversation_id
                }
                
                # 模拟一些工作
                time.sleep(0.1)
                
                # 再次检查ID是否还在
                final_id = get_current_conversation_id()
                results[thread_id]["final_id"] = final_id
                results[thread_id]["persistent"] = final_id == conversation_id
                
                # 记录日志
                logger = get_enhanced_logger(f"test_thread_{thread_id}")
                logger.info(f"线程 {thread_id} 的测试日志")
                
            except Exception as e:
                results[thread_id] = {"error": str(e)}
        
        # 创建多个线程，每个设置不同的conversation_id
        threads = []
        thread_count = 5
        
        for i in range(thread_count):
            conv_id = f"thread_{i}_{generate_conversation_id()}"
            thread = threading.Thread(target=worker_thread, args=(i, conv_id))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        all_success = True
        for thread_id, result in results.items():
            if "error" in result:
                self.log_test_result(
                    f"线程{thread_id}隔离测试",
                    False,
                    f"线程执行出错: {result['error']}"
                )
                all_success = False
            else:
                thread_success = result.get("success", False) and result.get("persistent", False)
                self.log_test_result(
                    f"线程{thread_id}隔离测试",
                    thread_success,
                    f"设置: {result['set_id'][:8]}..., 获取: {result['retrieved_id'][:8] if result['retrieved_id'] else None}..., 持久: {result['persistent']}"
                )
                all_success = all_success and thread_success
        
        self.log_test_result(
            "整体线程隔离测试",
            all_success,
            f"所有{thread_count}个线程的conversation_id都正确隔离"
        )
        
    def test_offline_task_protection(self):
        """测试离线任务保护"""
        print("\n=== 测试离线任务保护 ===")
        
        def simulate_offline_task(task_name: str):
            """模拟离线任务"""
            # 修改线程名称以模拟离线任务
            original_name = threading.current_thread().name
            threading.current_thread().name = task_name
            
            try:
                # 尝试设置conversation_id
                test_id = generate_conversation_id()
                set_current_conversation_id(test_id)
                
                # 检查is_offline_task函数
                is_offline = is_offline_task()
                
                # 检查safe_get_conversation_id函数
                safe_id = safe_get_conversation_id()
                
                return {
                    "is_offline_detected": is_offline,
                    "safe_id": safe_id,
                    "direct_id": get_current_conversation_id()
                }
            finally:
                # 恢复原始线程名称
                threading.current_thread().name = original_name
        
        # 测试不同类型的离线任务
        offline_tasks = [
            "token_refresh_worker",
            "session_cleanup_daemon",
            "db_monitor_background",
            "scheduler_timer"
        ]
        
        for task_name in offline_tasks:
            result = simulate_offline_task(task_name)
            
            self.log_test_result(
                f"离线任务检测-{task_name}",
                result["is_offline_detected"],
                f"任务名: {task_name}, 检测结果: {result['is_offline_detected']}"
            )
            
            self.log_test_result(
                f"安全获取保护-{task_name}",
                result["safe_id"] is None,
                f"安全获取应返回None，实际: {result['safe_id']}"
            )
        
    def test_context_manager(self):
        """测试上下文管理器"""
        print("\n=== 测试上下文管理器 ===")

        # 确保测试开始前状态干净
        clear_current_conversation_id()

        # 测试正常使用
        test_id = generate_conversation_id()

        # 上下文外应该没有conversation_id
        before_id = get_current_conversation_id()
        
        with conversation_context.conversation_scope(test_id):
            # 上下文内应该有conversation_id
            inside_id = get_current_conversation_id()
            self.logger.info("上下文管理器内的测试日志")
            
        # 上下文外应该又没有conversation_id
        after_id = get_current_conversation_id()
        
        self.log_test_result(
            "上下文管理器-进入前",
            before_id is None,
            f"进入前应为None，实际: {before_id}"
        )
        
        self.log_test_result(
            "上下文管理器-内部",
            inside_id == test_id,
            f"内部应为{test_id[:8]}...，实际: {inside_id[:8] if inside_id else None}..."
        )
        
        self.log_test_result(
            "上下文管理器-退出后",
            after_id is None,
            f"退出后应为None，实际: {after_id}"
        )
        
    def test_exception_handling(self):
        """测试异常处理"""
        print("\n=== 测试异常处理 ===")

        # 确保测试开始前状态干净
        clear_current_conversation_id()

        test_id = generate_conversation_id()
        
        try:
            with conversation_context.conversation_scope(test_id):
                # 在上下文内抛出异常
                raise ValueError("测试异常")
        except ValueError:
            pass  # 预期的异常
        
        # 检查异常后conversation_id是否被正确清理
        after_exception_id = get_current_conversation_id()
        
        self.log_test_result(
            "异常处理后清理",
            after_exception_id is None,
            f"异常后应为None，实际: {after_exception_id}"
        )
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始Conversation ID日志追踪功能测试")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行各项测试
        self.test_basic_functionality()
        self.test_thread_isolation()
        self.test_offline_task_protection()
        self.test_context_manager()
        self.test_exception_handling()
        
        end_time = time.time()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 测试结果汇总")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        print(f"执行时间: {end_time-start_time:.2f}秒")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        return failed_tests == 0


if __name__ == "__main__":
    tester = ConversationIdTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！Conversation ID日志追踪功能工作正常。")
        exit(0)
    else:
        print("\n💥 部分测试失败，请检查实现。")
        exit(1)
