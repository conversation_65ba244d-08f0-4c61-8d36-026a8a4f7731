"""
Enhanced Logger with Conversation ID Support

增强的日志记录器，自动在日志消息中包含conversation_id信息。
支持向后兼容，不影响现有代码的使用方式。

核心功能：
1. 自动在日志中包含conversation_id
2. 格式：[conversation_id: xxx] 原始消息
3. 向后兼容现有logger使用方式
4. 离线任务保护机制
"""

import logging
import sys
from typing import Optional


class ConversationFormatter(logging.Formatter):
    """
    支持conversation_id的日志格式化器
    
    自动在日志消息前添加[conversation_id: xxx]前缀，
    如果当前线程没有conversation_id则保持原格式。
    """
    
    def __init__(self, original_format: str, datefmt: str = None):
        """
        初始化格式化器
        
        Args:
            original_format: 原始的日志格式字符串
            datefmt: 日期格式字符串
        """
        super().__init__(original_format, datefmt)
        self.original_format = original_format
        
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录，自动添加conversation_id

        Args:
            record: 日志记录对象

        Returns:
            格式化后的日志字符串
        """
        # 延迟导入以避免循环导入
        try:
            from src.utils.conversation_context import safe_get_conversation_id
            # 获取当前conversation_id（安全方式，离线任务返回None）
            conversation_id = safe_get_conversation_id()
        except ImportError:
            # 如果导入失败，跳过conversation_id功能
            conversation_id = None

        # 如果有conversation_id，则在消息前添加前缀
        if conversation_id:
            # 保存原始消息
            original_msg = record.getMessage()
            # 添加conversation_id前缀
            enhanced_msg = f"[conversation_id: {conversation_id}] {original_msg}"
            # 临时修改record的消息
            record.msg = enhanced_msg
            record.args = ()  # 清空args，因为我们已经格式化了消息

        # 使用父类的格式化方法
        return super().format(record)


def create_enhanced_logger(logger_name: str = "chat_bi_mysql") -> logging.Logger:
    """
    创建增强的logger，支持conversation_id自动包含
    
    Args:
        logger_name: logger名称
        
    Returns:
        配置好的logger实例
    """
    # 获取或创建logger
    enhanced_logger = logging.getLogger(logger_name)
    
    # 如果已经配置过，直接返回
    if enhanced_logger.handlers and any(
        isinstance(handler.formatter, ConversationFormatter) 
        for handler in enhanced_logger.handlers
    ):
        return enhanced_logger
    
    # 清除现有的handlers（如果有的话）
    for handler in enhanced_logger.handlers[:]:
        enhanced_logger.removeHandler(handler)
    
    # 设置日志级别
    enhanced_logger.setLevel(logging.INFO)
    enhanced_logger.propagate = False
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # 创建增强的格式化器
    original_format = "[%(asctime)s] - %(levelname)s - %(name)s - %(message)s"
    enhanced_formatter = ConversationFormatter(
        original_format=original_format,
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(enhanced_formatter)
    
    # 添加处理器到logger
    enhanced_logger.addHandler(console_handler)
    
    return enhanced_logger


def upgrade_existing_logger(existing_logger: logging.Logger) -> logging.Logger:
    """
    升级现有的logger以支持conversation_id
    
    这个函数可以将现有的logger升级为支持conversation_id的版本，
    而不需要修改现有代码。
    
    Args:
        existing_logger: 现有的logger实例
        
    Returns:
        升级后的logger实例
    """
    # 检查是否已经升级过
    if any(isinstance(handler.formatter, ConversationFormatter) 
           for handler in existing_logger.handlers):
        return existing_logger
    
    # 升级所有handlers的formatter
    for handler in existing_logger.handlers:
        if isinstance(handler.formatter, logging.Formatter):
            # 获取原始格式
            original_format = handler.formatter._fmt
            datefmt = handler.formatter.datefmt
            
            # 创建增强的格式化器
            enhanced_formatter = ConversationFormatter(
                original_format=original_format,
                datefmt=datefmt
            )
            
            # 替换formatter
            handler.setFormatter(enhanced_formatter)
    
    return existing_logger


# 创建增强的全局logger实例
enhanced_logger = create_enhanced_logger("chat_bi_mysql")


def get_enhanced_logger(name: str = "chat_bi_mysql") -> logging.Logger:
    """
    获取增强的logger实例
    
    Args:
        name: logger名称
        
    Returns:
        增强的logger实例
    """
    if name == "chat_bi_mysql":
        return enhanced_logger
    else:
        return create_enhanced_logger(name)


# 向后兼容：提供与原logger相同的接口
def info(msg, *args, **kwargs):
    """向后兼容的info方法"""
    enhanced_logger.info(msg, *args, **kwargs)


def debug(msg, *args, **kwargs):
    """向后兼容的debug方法"""
    enhanced_logger.debug(msg, *args, **kwargs)


def warning(msg, *args, **kwargs):
    """向后兼容的warning方法"""
    enhanced_logger.warning(msg, *args, **kwargs)


def error(msg, *args, **kwargs):
    """向后兼容的error方法"""
    enhanced_logger.error(msg, *args, **kwargs)


def exception(msg, *args, **kwargs):
    """向后兼容的exception方法"""
    enhanced_logger.exception(msg, *args, **kwargs)


def critical(msg, *args, **kwargs):
    """向后兼容的critical方法"""
    enhanced_logger.critical(msg, *args, **kwargs)
